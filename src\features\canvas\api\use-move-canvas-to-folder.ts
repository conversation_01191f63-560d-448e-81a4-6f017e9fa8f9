import { useCallback } from 'react';
import type { Id } from '@/../convex/_generated/dataModel';
import { toast } from 'sonner';

export const useMoveCanvasToFolder = () => {
	const moveToFolder = useCallback(
		async (canvasId: Id<'messages'>, folder?: string) => {
			try {
				// This functionality is handled in the canvas page component
				// since we need access to the current message body to update it
				console.log('Move canvas to folder:', canvasId, folder);
				toast.success(
					folder ? `<PERSON><PERSON> moved to ${folder}` : 'Canvas moved to Unfiled'
				);
			} catch (error) {
				console.error('Failed to move canvas to folder:', error);
				toast.error('Failed to move canvas');
			}
		},
		[]
	);

	return { moveToFolder };
};
