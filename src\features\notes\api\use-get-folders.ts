import { useQuery } from 'convex/react';
import { useMemo } from 'react';

import { api } from '@/../convex/_generated/api';
import type { Id } from '@/../convex/_generated/dataModel';

export const useGetFolders = (
	workspaceId?: Id<'workspaces'>,
	channelId?: Id<'channels'>
) => {
	const folders = useQuery(
		api.notes.getFolders,
		workspaceId && channelId ? { workspaceId, channelId } : 'skip'
	);

	const data = useMemo(() => folders as string[] | undefined, [folders]);
	const isLoading = useMemo(() => folders === undefined, [folders]);

	return {
		data,
		isLoading,
	};
};
