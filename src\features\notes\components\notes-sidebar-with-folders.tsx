'use client';

import { useState, useMemo } from 'react';
import { Search, Plus, FileText, ChevronLeft, ChevronRight, Trash2, MoreHorizontal, Folder, FolderOpen } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { Id } from '@/../convex/_generated/dataModel';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { Note } from '../types';
import { FolderManager } from './folder-manager';

interface NotesSidebarWithFoldersProps {
  notes: Note[];
  folders: string[];
  selectedNoteId?: Id<'notes'> | null;
  onNoteSelect: (noteId: Id<'notes'>) => void;
  collapsed: boolean;
  onToggleCollapse: () => void;
  onCreateNote: (folder?: string) => void;
  onDeleteNote?: (noteId: Id<'notes'>) => void;
  onRenameNote?: (noteId: Id<'notes'>, newName: string) => void;
  onMoveToFolder?: (noteId: Id<'notes'>, folder?: string) => void;
  onCreateFolder?: (folderName: string) => void;
  workspaceId?: Id<'workspaces'>;
  channelId?: Id<'channels'>;
  className?: string;
}

export const NotesSidebarWithFolders = ({
  notes,
  folders,
  selectedNoteId,
  onNoteSelect,
  collapsed,
  onToggleCollapse,
  onCreateNote,
  onDeleteNote,
  onRenameNote,
  onMoveToFolder,
  onCreateFolder,
  workspaceId,
  channelId,
  className
}: NotesSidebarWithFoldersProps) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFolder, setSelectedFolder] = useState<string | null>(null);
  const [hoveredNoteId, setHoveredNoteId] = useState<Id<'notes'> | null>(null);
  const [openDropdownId, setOpenDropdownId] = useState<Id<'notes'> | null>(null);
  const [renamingNoteId, setRenamingNoteId] = useState<Id<'notes'> | null>(null);
  const [renameValue, setRenameValue] = useState('');

  // Filter and group notes
  const { filteredNotes, notesByFolder } = useMemo(() => {
    let filtered = notes;

    // Filter by search query
    if (searchQuery.trim()) {
      const searchLower = searchQuery.toLowerCase();
      filtered = notes.filter((note) => {
        const title = note.title || '';
        const content = note.content || '';
        const tags = note.tags || [];

        return (
          title.toLowerCase().includes(searchLower) ||
          content.toLowerCase().includes(searchLower) ||
          tags.some((tag: string) => tag.toLowerCase().includes(searchLower))
        );
      });
    }

    // Filter by selected folder
    if (selectedFolder !== null) {
      filtered = filtered.filter(note => note.folder === selectedFolder);
    }

    // Group by folder
    const grouped = filtered.reduce((acc, note) => {
      const folder = note.folder || 'Uncategorized';
      if (!acc[folder]) {
        acc[folder] = [];
      }
      acc[folder].push(note);
      return acc;
    }, {} as Record<string, Note[]>);

    return { filteredNotes: filtered, notesByFolder: grouped };
  }, [notes, searchQuery, selectedFolder]);

  // Get preview text for notes
  const getPreviewText = (content: string) => {
    if (!content) return '';

    try {
      const delta = JSON.parse(content);
      if (delta.ops) {
        return delta.ops
          .map((op: any) => (typeof op.insert === 'string' ? op.insert : ''))
          .join('')
          .replace(/\n/g, ' ')
          .trim()
          .substring(0, 100);
      }
    } catch {
      return content.substring(0, 100);
    }
    return '';
  };

  // Handle rename operations
  const handleRenameStart = (noteId: Id<'notes'>, currentTitle: string) => {
    setRenamingNoteId(noteId);
    setRenameValue(currentTitle);
    setOpenDropdownId(null);
  };

  const handleRenameSubmit = (noteId: Id<'notes'>) => {
    if (renameValue.trim() && onRenameNote) {
      onRenameNote(noteId, renameValue.trim());
    }
    setRenamingNoteId(null);
    setRenameValue('');
  };

  const handleRenameCancel = () => {
    setRenamingNoteId(null);
    setRenameValue('');
  };

  const handleRenameKeyDown = (e: React.KeyboardEvent, noteId: Id<'notes'>) => {
    if (e.key === 'Enter') {
      handleRenameSubmit(noteId);
    } else if (e.key === 'Escape') {
      handleRenameCancel();
    }
  };

  const handleCreateFolder = (folderName: string) => {
    if (onCreateFolder) {
      onCreateFolder(folderName);
    }
  };

  if (collapsed) {
    return (
      <div className={cn("w-12 border-r bg-muted/30 flex flex-col", className)}>
        <div className="p-2 border-b">
          <Button
            onClick={onToggleCollapse}
            variant="ghost"
            size="sm"
            className="w-full h-8 p-0"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

        <div className="p-2">
          <Button onClick={() => onCreateNote()} size="sm" className="w-full h-8 p-0">
            <Plus className="h-4 w-4" />
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("w-80 border-r bg-muted/30 flex flex-col", className)}>
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-3">
          <h2 className="font-semibold text-sm flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Notes
          </h2>

          <Button
            onClick={onToggleCollapse}
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
        </div>

        {/* Search and Create */}
        <div className="flex gap-2 mb-3">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search notes..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-9"
            />
          </div>
          <Button onClick={() => onCreateNote(selectedFolder || undefined)} size="sm">
            <Plus className="h-4 w-4" />
          </Button>
        </div>

        {/* Folder Manager */}
        <FolderManager
          folders={folders}
          selectedFolder={selectedFolder}
          onFolderSelect={setSelectedFolder}
          onCreateFolder={handleCreateFolder}
        />
      </div>

      {/* Notes List */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-2">
          {filteredNotes.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-sm text-muted-foreground">
                {searchQuery ? 'No notes found' : selectedFolder ? `No notes in "${selectedFolder}"` : 'No notes yet'}
              </p>
              {!searchQuery && (
                <Button
                  onClick={() => onCreateNote(selectedFolder || undefined)}
                  variant="outline"
                  size="sm"
                  className="mt-2"
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Create Note
                </Button>
              )}
            </div>
          ) : selectedFolder === null ? (
            // Show grouped by folders when no specific folder is selected
            <div className="space-y-4">
              {Object.entries(notesByFolder).map(([folder, folderNotes]) => (
                <div key={folder} className="space-y-1">
                  <div className="flex items-center gap-2 px-2 py-1 text-xs font-medium text-muted-foreground">
                    <Folder className="h-3 w-3" />
                    {folder}
                  </div>
                  {folderNotes.map((note) => (
                    <NoteItem
                      key={note._id}
                      note={note}
                      isSelected={selectedNoteId === note._id}
                      isHovered={hoveredNoteId === note._id}
                      isRenaming={renamingNoteId === note._id}
                      renameValue={renameValue}
                      onSelect={() => onNoteSelect(note._id)}
                      onHover={() => setHoveredNoteId(note._id)}
                      onHoverEnd={() => {
                        if (openDropdownId !== note._id) {
                          setHoveredNoteId(null);
                        }
                      }}
                      onRenameStart={() => handleRenameStart(note._id, note.title)}
                      onRenameSubmit={() => handleRenameSubmit(note._id)}
                      onRenameCancel={handleRenameCancel}
                      onRenameKeyDown={(e) => handleRenameKeyDown(e, note._id)}
                      onRenameValueChange={setRenameValue}
                      onDelete={() => onDeleteNote?.(note._id)}
                      onMoveToFolder={(folder) => onMoveToFolder?.(note._id, folder)}
                      folders={folders}
                      getPreviewText={getPreviewText}
                      openDropdownId={openDropdownId}
                      setOpenDropdownId={setOpenDropdownId}
                    />
                  ))}
                </div>
              ))}
            </div>
          ) : (
            // Show flat list when a specific folder is selected
            <div className="space-y-1">
              {filteredNotes.map((note) => (
                <NoteItem
                  key={note._id}
                  note={note}
                  isSelected={selectedNoteId === note._id}
                  isHovered={hoveredNoteId === note._id}
                  isRenaming={renamingNoteId === note._id}
                  renameValue={renameValue}
                  onSelect={() => onNoteSelect(note._id)}
                  onHover={() => setHoveredNoteId(note._id)}
                  onHoverEnd={() => {
                    if (openDropdownId !== note._id) {
                      setHoveredNoteId(null);
                    }
                  }}
                  onRenameStart={() => handleRenameStart(note._id, note.title)}
                  onRenameSubmit={() => handleRenameSubmit(note._id)}
                  onRenameCancel={handleRenameCancel}
                  onRenameKeyDown={(e) => handleRenameKeyDown(e, note._id)}
                  onRenameValueChange={setRenameValue}
                  onDelete={() => onDeleteNote?.(note._id)}
                  onMoveToFolder={(folder) => onMoveToFolder?.(note._id, folder)}
                  folders={folders}
                  getPreviewText={getPreviewText}
                  openDropdownId={openDropdownId}
                  setOpenDropdownId={setOpenDropdownId}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Note Item Component
interface NoteItemProps {
  note: Note;
  isSelected: boolean;
  isHovered: boolean;
  isRenaming: boolean;
  renameValue: string;
  onSelect: () => void;
  onHover: () => void;
  onHoverEnd: () => void;
  onRenameStart: () => void;
  onRenameSubmit: () => void;
  onRenameCancel: () => void;
  onRenameKeyDown: (e: React.KeyboardEvent) => void;
  onRenameValueChange: (value: string) => void;
  onDelete: () => void;
  onMoveToFolder: (folder?: string) => void;
  folders: string[];
  getPreviewText: (content: string) => string;
  openDropdownId: Id<'notes'> | null;
  setOpenDropdownId: (id: Id<'notes'> | null) => void;
}

const NoteItem = ({
  note,
  isSelected,
  isHovered,
  isRenaming,
  renameValue,
  onSelect,
  onHover,
  onHoverEnd,
  onRenameStart,
  onRenameSubmit,
  onRenameCancel,
  onRenameKeyDown,
  onRenameValueChange,
  onDelete,
  onMoveToFolder,
  folders,
  getPreviewText,
  openDropdownId,
  setOpenDropdownId,
}: NoteItemProps) => {
  return (
    <div
      onClick={onSelect}
      onMouseEnter={onHover}
      onMouseLeave={onHoverEnd}
      className={cn(
        "p-3 rounded-lg cursor-pointer transition-colors group",
        isSelected
          ? "bg-primary/10 border border-primary/20"
          : "hover:bg-muted/50"
      )}
    >
      <div className="flex items-start justify-between gap-2">
        <div className="flex-1 min-w-0">
          {isRenaming ? (
            <Input
              value={renameValue}
              onChange={(e) => onRenameValueChange(e.target.value)}
              onKeyDown={onRenameKeyDown}
              onBlur={onRenameSubmit}
              autoFocus
              className="h-6 text-sm font-medium border-none shadow-none p-0 focus-visible:ring-0"
              onClick={(e) => e.stopPropagation()}
            />
          ) : (
            <h3 className="font-medium text-sm truncate">
              {note.title || 'Untitled Note'}
            </h3>
          )}

          {note.content && (
            <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
              {getPreviewText(note.content)}
            </p>
          )}

          {note.tags && note.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-2">
              {note.tags.slice(0, 3).map((tag, index) => (
                <Badge key={index} variant="secondary" className="text-xs px-1 py-0">
                  {tag}
                </Badge>
              ))}
              {note.tags.length > 3 && (
                <Badge variant="secondary" className="text-xs px-1 py-0">
                  +{note.tags.length - 3}
                </Badge>
              )}
            </div>
          )}
        </div>

        {(isHovered || openDropdownId === note._id) && (
          <DropdownMenu
            open={openDropdownId === note._id}
            onOpenChange={(open) => setOpenDropdownId(open ? note._id : null)}
          >
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={(e) => e.stopPropagation()}
              >
                <MoreHorizontal className="h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={(e) => {
                e.stopPropagation();
                onRenameStart();
              }}>
                Rename
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  onMoveToFolder(undefined);
                }}
              >
                Move to Root
              </DropdownMenuItem>
              {folders.map((folder) => (
                <DropdownMenuItem
                  key={folder}
                  onClick={(e) => {
                    e.stopPropagation();
                    onMoveToFolder(folder);
                  }}
                >
                  Move to "{folder}"
                </DropdownMenuItem>
              ))}
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete();
                }}
                className="text-destructive"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
    </div>
  );
};
