'use client';

import React, { useState } from 'react';
import { Folder, Plus, X, MoreHorizontal, FolderOpen } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface FolderManagerProps {
  folders: string[];
  selectedFolder: string | null;
  onFolderSelect: (folder: string | null) => void;
  onCreateFolder: (folderName: string) => void;
  className?: string;
}

export const FolderManager = ({
  folders,
  selectedFolder,
  onFolderSelect,
  onCreateFolder,
  className
}: FolderManagerProps) => {
  const [isCreating, setIsCreating] = useState(false);
  const [newFolderName, setNewFolderName] = useState('');

  const handleCreateFolder = () => {
    if (newFolderName.trim()) {
      onCreateFolder(newFolderName.trim());
      setNewFolderName('');
      setIsCreating(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleCreateFolder();
    } else if (e.key === 'Escape') {
      setIsCreating(false);
      setNewFolderName('');
    }
  };

  return (
    <div className={cn("space-y-2", className)}>
      {/* All Notes Option */}
      <div
        onClick={() => onFolderSelect(null)}
        className={cn(
          "flex items-center gap-2 px-3 py-2 rounded-lg cursor-pointer transition-colors",
          selectedFolder === null
            ? "bg-primary/10 border border-primary/20"
            : "hover:bg-muted/50"
        )}
      >
        <FolderOpen className="h-4 w-4" />
        <span className="text-sm font-medium">All Notes</span>
      </div>

      {/* Existing Folders */}
      {folders.map((folder) => (
        <div
          key={folder}
          onClick={() => onFolderSelect(folder)}
          className={cn(
            "flex items-center justify-between gap-2 px-3 py-2 rounded-lg cursor-pointer transition-colors group",
            selectedFolder === folder
              ? "bg-primary/10 border border-primary/20"
              : "hover:bg-muted/50"
          )}
        >
          <div className="flex items-center gap-2 flex-1 min-w-0">
            <Folder className="h-4 w-4 flex-shrink-0" />
            <span className="text-sm font-medium truncate">{folder}</span>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={(e) => e.stopPropagation()}
              >
                <MoreHorizontal className="h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  // TODO: Implement rename folder
                }}
              >
                Rename Folder
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  // TODO: Implement delete folder
                }}
                className="text-destructive"
              >
                Delete Folder
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ))}

      {/* Create New Folder */}
      {isCreating ? (
        <div className="flex items-center gap-2 px-3 py-2">
          <Folder className="h-4 w-4" />
          <Input
            value={newFolderName}
            onChange={(e) => setNewFolderName(e.target.value)}
            onKeyDown={handleKeyDown}
            onBlur={handleCreateFolder}
            placeholder="Folder name..."
            className="h-6 text-sm border-none shadow-none p-0 focus-visible:ring-0"
            autoFocus
          />
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={() => {
              setIsCreating(false);
              setNewFolderName('');
            }}
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      ) : (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsCreating(true)}
          className="w-full justify-start gap-2 h-8 text-muted-foreground hover:text-foreground"
        >
          <Plus className="h-4 w-4" />
          New Folder
        </Button>
      )}
    </div>
  );
};
