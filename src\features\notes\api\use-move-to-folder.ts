import { useMutation } from 'convex/react';
import { useCallback } from 'react';
import { toast } from 'sonner';

import { api } from '@/../convex/_generated/api';
import type { Id } from '@/../convex/_generated/dataModel';

export const useMoveToFolder = () => {
	const mutate = useMutation(api.notes.moveToFolder);

	const moveToFolder = useCallback(
		async (noteId: Id<'notes'>, folder?: string) => {
			try {
				await mutate({
					noteId,
					folder,
				});
				toast.success('Note moved successfully');
			} catch (error) {
				console.error('Failed to move note:', error);
				toast.error('Failed to move note');
				throw error;
			}
		},
		[mutate]
	);

	return { moveToFolder };
};
