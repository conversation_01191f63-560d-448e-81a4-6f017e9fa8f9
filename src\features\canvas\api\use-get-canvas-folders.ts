import { useQuery } from 'convex/react';
import { useMemo } from 'react';
import { api } from '@/../convex/_generated/api';
import type { Id } from '@/../convex/_generated/dataModel';

interface UseGetCanvasFoldersProps {
  workspaceId?: Id<'workspaces'>;
  channelId?: Id<'channels'>;
}

export const useGetCanvasFolders = ({ workspaceId, channelId }: UseGetCanvasFoldersProps) => {
  // Get messages from the channel to find canvas items
  const messages = useQuery(
    api.messages.get,
    channelId ? {
      channelId: channelId,
      paginationOpts: {
        numItems: 1000, // Get more messages to find all canvases
        cursor: null
      }
    } : "skip"
  );

  // Extract unique folders from canvas messages
  const folders = useMemo(() => {
    const folderSet = new Set<string>();
    
    if (messages?.page) {
      messages.page.forEach((message) => {
        try {
          const body = JSON.parse(message.body);
          if (body.type === "canvas" && body.folder) {
            folderSet.add(body.folder);
          }
        } catch (error) {
          // Skip invalid JSON
        }
      });
    }
    
    return Array.from(folderSet).sort();
  }, [messages]);

  const isLoading = messages === undefined;

  return {
    data: folders,
    isLoading,
  };
};
