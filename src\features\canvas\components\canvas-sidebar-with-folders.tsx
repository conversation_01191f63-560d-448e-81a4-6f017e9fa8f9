"use client";

import React, { useState, useMemo } from 'react';
import { Search, Plus, FolderPlus, ChevronDown, ChevronRight, FileImage, MoreHorizontal, Edit2, Trash2, FolderOpen, Folder } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';
import { FolderManager } from '@/features/notes/components/folder-manager';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import type { Id } from '@/../convex/_generated/dataModel';

// Canvas interface based on message structure
interface Canvas {
  _id: Id<'messages'>;
  body: string;
  canvasName: string;
  roomId: string;
  savedCanvasId: string;
  createdAt: number;
  updatedAt: number;
  tags?: string[];
  folder?: string;
}

interface CanvasSidebarWithFoldersProps {
  canvases: Canvas[];
  folders: string[];
  selectedCanvasId?: string | null;
  onCanvasSelect: (canvasId: string) => void;
  collapsed: boolean;
  onToggleCollapse: () => void;
  onCreateCanvas: (folder?: string) => void;
  onDeleteCanvas?: (canvasId: Id<'messages'>) => void;
  onRenameCanvas?: (canvasId: Id<'messages'>, newName: string) => void;
  onMoveToFolder?: (canvasId: Id<'messages'>, folder?: string) => void;
  onCreateFolder?: (folderName: string) => void;
  workspaceId?: Id<'workspaces'>;
  channelId?: Id<'channels'>;
  className?: string;
}

// Format creation time
const formatTime = (timestamp: number) => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

  if (diffInHours < 24) {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  } else if (diffInHours < 24 * 7) {
    return date.toLocaleDateString([], { weekday: 'short', hour: '2-digit', minute: '2-digit' });
  } else {
    return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
  }
};

export const CanvasSidebarWithFolders = ({
  canvases,
  folders,
  selectedCanvasId,
  onCanvasSelect,
  collapsed,
  onToggleCollapse,
  onCreateCanvas,
  onDeleteCanvas,
  onRenameCanvas,
  onMoveToFolder,
  onCreateFolder,
  workspaceId,
  channelId,
  className,
}: CanvasSidebarWithFoldersProps) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFolder, setSelectedFolder] = useState<string | null>(null);
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set());
  const [hoveredCanvasId, setHoveredCanvasId] = useState<Id<'messages'> | null>(null);
  const [openDropdownId, setOpenDropdownId] = useState<Id<'messages'> | null>(null);
  const [renamingCanvasId, setRenamingCanvasId] = useState<Id<'messages'> | null>(null);
  const [renameValue, setRenameValue] = useState('');

  // Filter canvases based on search query
  const filteredCanvases = useMemo(() => {
    return canvases.filter((canvas) =>
      canvas.canvasName.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [canvases, searchQuery]);

  // Group canvases by folder
  const canvasesByFolder = useMemo(() => {
    const grouped: Record<string, Canvas[]> = {};

    filteredCanvases.forEach((canvas) => {
      const folder = canvas.folder || 'Unfiled';
      if (!grouped[folder]) {
        grouped[folder] = [];
      }
      grouped[folder].push(canvas);
    });

    return grouped;
  }, [filteredCanvases]);

  // Get preview text from canvas content
  const getPreviewText = (content: string): string => {
    try {
      const parsed = JSON.parse(content);
      return parsed.canvasName || 'Untitled Canvas';
    } catch {
      return 'Untitled Canvas';
    }
  };

  // Handle folder toggle
  const toggleFolder = (folderName: string) => {
    const newExpanded = new Set(expandedFolders);
    if (newExpanded.has(folderName)) {
      newExpanded.delete(folderName);
    } else {
      newExpanded.add(folderName);
    }
    setExpandedFolders(newExpanded);
  };

  // Handle folder creation
  const handleCreateFolder = (folderName: string) => {
    onCreateFolder?.(folderName);
  };

  // Handle canvas creation in folder
  const handleCreateCanvasInFolder = (folder?: string) => {
    onCreateCanvas(folder);
  };

  // Handle rename operations
  const handleRenameStart = (canvasId: Id<'messages'>, currentTitle: string) => {
    setRenamingCanvasId(canvasId);
    setRenameValue(currentTitle);
    setOpenDropdownId(null);
  };

  const handleRenameSubmit = (canvasId: Id<'messages'>) => {
    if (renameValue.trim() && onRenameCanvas) {
      onRenameCanvas(canvasId, renameValue.trim());
    }
    setRenamingCanvasId(null);
    setRenameValue('');
  };

  const handleRenameCancel = () => {
    setRenamingCanvasId(null);
    setRenameValue('');
  };

  const handleRenameKeyDown = (e: React.KeyboardEvent, canvasId: Id<'messages'>) => {
    if (e.key === 'Enter') {
      handleRenameSubmit(canvasId);
    } else if (e.key === 'Escape') {
      handleRenameCancel();
    }
  };

  if (collapsed) {
    return (
      <div className={cn('w-16 border-r bg-background flex flex-col', className)}>
        <div className="p-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={onToggleCollapse}
            className="w-8 h-8"
          >
            <FileImage className="h-4 w-4" />
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('w-80 border-r bg-background flex flex-col', className)}>
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={onToggleCollapse}
              className="w-8 h-8"
            >
              <FileImage className="h-4 w-4" />
            </Button>
            <h2 className="font-semibold">Canvas</h2>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => onCreateCanvas()}
            className="w-8 h-8"
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>

        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search canvases..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>

        {/* Folder Manager */}
        <div className="mt-4">
          <FolderManager
            folders={folders}
            selectedFolder={selectedFolder}
            onFolderSelect={setSelectedFolder}
            onCreateFolder={handleCreateFolder}
          />
        </div>
      </div>

      {/* Canvas List */}
      <ScrollArea className="flex-1">
        <div className="p-2">
          {Object.keys(canvasesByFolder).length === 0 ? (
            <div className="text-center py-8">
              <FileImage className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-sm text-muted-foreground">
                {searchQuery ? 'No canvases match your search' : 'No canvases yet'}
              </p>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onCreateCanvas()}
                className="mt-2"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Canvas
              </Button>
            </div>
          ) : selectedFolder ? (
            // Show canvases from selected folder only
            <div className="space-y-1">
              {(canvasesByFolder[selectedFolder] || []).map((canvas) => (
                <CanvasItem
                  key={canvas._id}
                  canvas={canvas}
                  isSelected={selectedCanvasId === canvas.savedCanvasId || selectedCanvasId === canvas._id}
                  isHovered={hoveredCanvasId === canvas._id}
                  isRenaming={renamingCanvasId === canvas._id}
                  renameValue={renameValue}
                  onSelect={() => onCanvasSelect(canvas.savedCanvasId || canvas._id)}
                  onHover={() => setHoveredCanvasId(canvas._id)}
                  onHoverEnd={() => {
                    if (openDropdownId !== canvas._id) {
                      setHoveredCanvasId(null);
                    }
                  }}
                  onRenameStart={() => handleRenameStart(canvas._id, canvas.canvasName)}
                  onRenameSubmit={() => handleRenameSubmit(canvas._id)}
                  onRenameCancel={handleRenameCancel}
                  onRenameKeyDown={(e) => handleRenameKeyDown(e, canvas._id)}
                  onRenameValueChange={setRenameValue}
                  onDelete={() => onDeleteCanvas?.(canvas._id)}
                  onMoveToFolder={(folder) => onMoveToFolder?.(canvas._id, folder)}
                  folders={folders}
                  getPreviewText={getPreviewText}
                  openDropdownId={openDropdownId}
                  setOpenDropdownId={setOpenDropdownId}
                />
              ))}
            </div>
          ) : (
            // Show grouped by folders
            <div className="space-y-2">
              {Object.entries(canvasesByFolder)
                .sort(([a], [b]) => {
                  if (a === 'Unfiled') return 1;
                  if (b === 'Unfiled') return -1;
                  return a.localeCompare(b);
                })
                .map(([folderName, folderCanvases]) => (
                  <div key={folderName} className="space-y-1">
                    {/* Folder Header */}
                    <div className="flex items-center justify-between group">
                      <button
                        onClick={() => toggleFolder(folderName)}
                        className="flex items-center gap-2 text-sm font-medium text-muted-foreground hover:text-foreground transition-colors flex-1"
                      >
                        {expandedFolders.has(folderName) ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                        {expandedFolders.has(folderName) ? (
                          <FolderOpen className="h-4 w-4" />
                        ) : (
                          <Folder className="h-4 w-4" />
                        )}
                        <span>{folderName}</span>
                        <span className="text-xs">({folderCanvases.length})</span>
                      </button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleCreateCanvasInFolder(folderName === 'Unfiled' ? undefined : folderName)}
                        className="w-6 h-6 opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <Plus className="h-3 w-3" />
                      </Button>
                    </div>

                    {/* Folder Contents */}
                    {expandedFolders.has(folderName) && (
                      <div className="ml-6 space-y-1">
                        {folderCanvases.map((canvas) => (
                          <CanvasItem
                            key={canvas._id}
                            canvas={canvas}
                            isSelected={selectedCanvasId === canvas.savedCanvasId || selectedCanvasId === canvas._id}
                            isHovered={hoveredCanvasId === canvas._id}
                            isRenaming={renamingCanvasId === canvas._id}
                            renameValue={renameValue}
                            onSelect={() => onCanvasSelect(canvas.savedCanvasId || canvas._id)}
                            onHover={() => setHoveredCanvasId(canvas._id)}
                            onHoverEnd={() => {
                              if (openDropdownId !== canvas._id) {
                                setHoveredCanvasId(null);
                              }
                            }}
                            onRenameStart={() => handleRenameStart(canvas._id, canvas.canvasName)}
                            onRenameSubmit={() => handleRenameSubmit(canvas._id)}
                            onRenameCancel={handleRenameCancel}
                            onRenameKeyDown={(e) => handleRenameKeyDown(e, canvas._id)}
                            onRenameValueChange={setRenameValue}
                            onDelete={() => onDeleteCanvas?.(canvas._id)}
                            onMoveToFolder={(folder) => onMoveToFolder?.(canvas._id, folder)}
                            folders={folders}
                            getPreviewText={getPreviewText}
                            openDropdownId={openDropdownId}
                            setOpenDropdownId={setOpenDropdownId}
                          />
                        ))}
                      </div>
                    )}
                  </div>
                ))}
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
};

// Canvas Item Component
interface CanvasItemProps {
  canvas: Canvas;
  isSelected: boolean;
  isHovered: boolean;
  isRenaming: boolean;
  renameValue: string;
  onSelect: () => void;
  onHover: () => void;
  onHoverEnd: () => void;
  onRenameStart: () => void;
  onRenameSubmit: () => void;
  onRenameCancel: () => void;
  onRenameKeyDown: (e: React.KeyboardEvent) => void;
  onRenameValueChange: (value: string) => void;
  onDelete: () => void;
  onMoveToFolder: (folder?: string) => void;
  folders: string[];
  getPreviewText: (content: string) => string;
  openDropdownId: Id<'messages'> | null;
  setOpenDropdownId: (id: Id<'messages'> | null) => void;
}

const CanvasItem = ({
  canvas,
  isSelected,
  isHovered,
  isRenaming,
  renameValue,
  onSelect,
  onHover,
  onHoverEnd,
  onRenameStart,
  onRenameSubmit,
  onRenameCancel,
  onRenameKeyDown,
  onRenameValueChange,
  onDelete,
  onMoveToFolder,
  folders,
  getPreviewText,
  openDropdownId,
  setOpenDropdownId,
}: CanvasItemProps) => {
  return (
    <div
      className={cn(
        'group relative rounded-lg border p-3 transition-all duration-200 cursor-pointer',
        isSelected
          ? 'bg-primary/10 border-primary/20 shadow-sm'
          : 'bg-background border-border hover:bg-muted/50 hover:shadow-sm'
      )}
      onMouseEnter={onHover}
      onMouseLeave={onHoverEnd}
      onClick={onSelect}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          {isRenaming ? (
            <Input
              value={renameValue}
              onChange={(e) => onRenameValueChange(e.target.value)}
              onKeyDown={onRenameKeyDown}
              onBlur={onRenameCancel}
              className="h-6 text-sm font-medium"
              autoFocus
            />
          ) : (
            <h3 className="font-medium text-sm truncate mb-1">
              {canvas.canvasName}
            </h3>
          )}
          <p className="text-xs text-muted-foreground">
            {formatTime(canvas.createdAt)}
          </p>
        </div>

        <div className="flex items-center gap-1 ml-2">
          <FileImage className="h-4 w-4 text-muted-foreground flex-shrink-0" />

          {(isHovered || openDropdownId === canvas._id) && !isRenaming && (
            <DropdownMenu
              open={openDropdownId === canvas._id}
              onOpenChange={(open) => setOpenDropdownId(open ? canvas._id : null)}
            >
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="w-6 h-6 opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={(e) => {
                    e.stopPropagation();
                    setOpenDropdownId(openDropdownId === canvas._id ? null : canvas._id);
                  }}
                >
                  <MoreHorizontal className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem onClick={(e) => {
                  e.stopPropagation();
                  onRenameStart();
                }}>
                  <Edit2 className="h-4 w-4 mr-2" />
                  Rename
                </DropdownMenuItem>

                <DropdownMenuSeparator />

                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    onMoveToFolder(undefined);
                  }}
                  className="text-muted-foreground"
                >
                  <Folder className="h-4 w-4 mr-2" />
                  Move to Unfiled
                </DropdownMenuItem>

                {folders.map((folder) => (
                  <DropdownMenuItem
                    key={folder}
                    onClick={(e) => {
                      e.stopPropagation();
                      onMoveToFolder(folder);
                    }}
                    className="text-muted-foreground"
                  >
                    <Folder className="h-4 w-4 mr-2" />
                    Move to {folder}
                  </DropdownMenuItem>
                ))}

                <DropdownMenuSeparator />

                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    onDelete();
                  }}
                  className="text-destructive focus:text-destructive"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </div>
    </div>
  );
};
